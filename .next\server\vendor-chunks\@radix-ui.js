"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zLWd5bS1lbGl0ZS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzP2I4ZWMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Label,Root auto */ // src/label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label, {\n        ...props,\n        ref: forwardedRef,\n        onMouseDown: (event)=>{\n            const target = event.target;\n            if (target.closest(\"button, input, select, textarea\")) return;\n            props.onMouseDown?.(event);\n            if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n        }\n    });\n});\nLabel.displayName = NAME;\nvar Root = Label;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXBvcnRhbC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUF1QjtBQUNGO0FBQ0s7QUFDTTtBQXVCSjtBQWpCNUIsSUFBTUssY0FBYztBQVdwQixJQUFNQyx1QkFBZU4sNkNBQUEsQ0FBdUMsQ0FBQ1EsT0FBT0M7SUFDbEUsTUFBTSxFQUFFQyxXQUFXQyxhQUFBLEVBQWUsR0FBR0MsYUFBWSxHQUFJSjtJQUNyRCxNQUFNLENBQUNLLFNBQVNDLFdBQVUsR0FBVWQsMkNBQUEsQ0FBUztJQUM3Q0csa0ZBQWVBLENBQUMsSUFBTVcsV0FBVyxPQUFPLEVBQUU7SUFDMUMsTUFBTUosWUFBWUMsaUJBQWtCRSxXQUFXRyxZQUFZQyxVQUFVQztJQUNyRSxPQUFPUiwwQkFDSFQsbURBQVMsQ0FBYSxnQkFBQUcsc0RBQUFBLENBQUNGLGdFQUFTQSxDQUFDa0IsR0FBQSxFQUFWO1FBQWUsR0FBR1IsV0FBQTtRQUFhUyxLQUFLWjtJQUFBLElBQWtCQyxhQUM3RTtBQUNOO0FBRUFKLE9BQU9nQixXQUFBLEdBQWNqQjtBQUlyQixJQUFNa0IsT0FBT2pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zLWd5bS1lbGl0ZS8uLi9zcmMvcG9ydGFsLnRzeD9mYmEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBSZWFjdERPTSBmcm9tICdyZWFjdC1kb20nO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZSc7XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3QnO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBQb3J0YWxcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgUE9SVEFMX05BTUUgPSAnUG9ydGFsJztcblxudHlwZSBQb3J0YWxFbGVtZW50ID0gUmVhY3QuQ29tcG9uZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuZGl2PjtcbnR5cGUgUHJpbWl0aXZlRGl2UHJvcHMgPSBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFByaW1pdGl2ZS5kaXY+O1xuaW50ZXJmYWNlIFBvcnRhbFByb3BzIGV4dGVuZHMgUHJpbWl0aXZlRGl2UHJvcHMge1xuICAvKipcbiAgICogQW4gb3B0aW9uYWwgY29udGFpbmVyIHdoZXJlIHRoZSBwb3J0YWxlZCBjb250ZW50IHNob3VsZCBiZSBhcHBlbmRlZC5cbiAgICovXG4gIGNvbnRhaW5lcj86IEVsZW1lbnQgfCBEb2N1bWVudEZyYWdtZW50IHwgbnVsbDtcbn1cblxuY29uc3QgUG9ydGFsID0gUmVhY3QuZm9yd2FyZFJlZjxQb3J0YWxFbGVtZW50LCBQb3J0YWxQcm9wcz4oKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgY29uc3QgeyBjb250YWluZXI6IGNvbnRhaW5lclByb3AsIC4uLnBvcnRhbFByb3BzIH0gPSBwcm9wcztcbiAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4gc2V0TW91bnRlZCh0cnVlKSwgW10pO1xuICBjb25zdCBjb250YWluZXIgPSBjb250YWluZXJQcm9wIHx8IChtb3VudGVkICYmIGdsb2JhbFRoaXM/LmRvY3VtZW50Py5ib2R5KTtcbiAgcmV0dXJuIGNvbnRhaW5lclxuICAgID8gUmVhY3RET00uY3JlYXRlUG9ydGFsKDxQcmltaXRpdmUuZGl2IHsuLi5wb3J0YWxQcm9wc30gcmVmPXtmb3J3YXJkZWRSZWZ9IC8+LCBjb250YWluZXIpXG4gICAgOiBudWxsO1xufSk7XG5cblBvcnRhbC5kaXNwbGF5TmFtZSA9IFBPUlRBTF9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IFJvb3QgPSBQb3J0YWw7XG5cbmV4cG9ydCB7XG4gIFBvcnRhbCxcbiAgLy9cbiAgUm9vdCxcbn07XG5leHBvcnQgdHlwZSB7IFBvcnRhbFByb3BzIH07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJSZWFjdERPTSIsIlByaW1pdGl2ZSIsInVzZUxheW91dEVmZmVjdCIsImpzeCIsIlBPUlRBTF9OQU1FIiwiUG9ydGFsIiwiZm9yd2FyZFJlZiIsInByb3BzIiwiZm9yd2FyZGVkUmVmIiwiY29udGFpbmVyIiwiY29udGFpbmVyUHJvcCIsInBvcnRhbFByb3BzIiwibW91bnRlZCIsInNldE1vdW50ZWQiLCJ1c2VTdGF0ZSIsImdsb2JhbFRoaXMiLCJkb2N1bWVudCIsImJvZHkiLCJjcmVhdGVQb3J0YWwiLCJkaXYiLCJyZWYiLCJkaXNwbGF5TmFtZSIsIlJvb3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            stylesRef.current = node2 ? getComputedStyle(node2) : null;\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount + 1), []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount - 1), []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) ref.current?.focus();\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const wrapper = wrapperRef.current;\n        const viewport = ref.current;\n        if (hasToasts && wrapper && viewport) {\n            const handlePause = ()=>{\n                if (!context.isClosePausedRef.current) {\n                    const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                    viewport.dispatchEvent(pauseEvent);\n                    context.isClosePausedRef.current = true;\n                }\n            };\n            const handleResume = ()=>{\n                if (context.isClosePausedRef.current) {\n                    const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                    viewport.dispatchEvent(resumeEvent);\n                    context.isClosePausedRef.current = false;\n                }\n            };\n            const handleFocusOutResume = (event)=>{\n                const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                if (isFocusMovingOutside) handleResume();\n            };\n            const handlePointerLeaveResume = ()=>{\n                const isFocusInside = wrapper.contains(document.activeElement);\n                if (!isFocusInside) handleResume();\n            };\n            wrapper.addEventListener(\"focusin\", handlePause);\n            wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n            wrapper.addEventListener(\"pointermove\", handlePause);\n            wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n            window.addEventListener(\"blur\", handlePause);\n            window.addEventListener(\"focus\", handleResume);\n            return ()=>{\n                wrapper.removeEventListener(\"focusin\", handlePause);\n                wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.removeEventListener(\"pointermove\", handlePause);\n                wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.removeEventListener(\"blur\", handlePause);\n                window.removeEventListener(\"focus\", handleResume);\n            };\n        }\n    }, [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(({ tabbingDirection })=>{\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem)=>{\n            const toastNode = toastItem.ref.current;\n            const toastTabbableCandidates = [\n                toastNode,\n                ...getTabbableCandidates(toastNode)\n            ];\n            return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n    }, [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = ref.current;\n        if (viewport) {\n            const handleKeyDown = (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const focusedElement = document.activeElement;\n                    const isTabbingBackwards = event.shiftKey;\n                    const targetIsViewport = event.target === viewport;\n                    if (targetIsViewport && isTabbingBackwards) {\n                        headFocusProxyRef.current?.focus();\n                        return;\n                    }\n                    const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                    const sortedCandidates = getSortedTabbableCandidates({\n                        tabbingDirection\n                    });\n                    const index = sortedCandidates.findIndex((candidate)=>candidate === focusedElement);\n                    if (focusFirst(sortedCandidates.slice(index + 1))) {\n                        event.preventDefault();\n                    } else {\n                        isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                    }\n                }\n            };\n            viewport.addEventListener(\"keydown\", handleKeyDown);\n            return ()=>viewport.removeEventListener(\"keydown\", handleKeyDown);\n        }\n    }, [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? true,\n        onChange: onOpenChange,\n        caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(()=>{\n        const isFocusInToast = node?.contains(document.activeElement);\n        if (isFocusInToast) context.viewport?.focus();\n        onClose();\n    });\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((duration2)=>{\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n    }, [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        if (viewport) {\n            const handleResume = ()=>{\n                startTimer(closeTimerRemainingTimeRef.current);\n                onResume?.();\n            };\n            const handlePause = ()=>{\n                const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                window.clearTimeout(closeTimerRef.current);\n                onPause?.();\n            };\n            viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n            viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n            return ()=>{\n                viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n            };\n        }\n    }, [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onToastAdd();\n        return ()=>onToastRemove();\n    }, [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return node ? getAnnounceTextContent(node) : null;\n    }, [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame(()=>setRenderAnnounceText(true));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const timer = window.setTimeout(()=>setIsAnnounced(true), 1e3);\n        return ()=>window.clearTimeout(timer);\n    }, []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)(()=>{\n        let raf1 = 0;\n        let raf2 = 0;\n        raf1 = window.requestAnimationFrame(()=>raf2 = window.requestAnimationFrame(fn));\n        return ()=>{\n            window.cancelAnimationFrame(raf1);\n            window.cancelAnimationFrame(raf2);\n        };\n    }, [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zLWd5bS1lbGl0ZS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZi9kaXN0L2luZGV4Lm1qcz8xMjMyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jYWxsYmFjay1yZWYvc3JjL3VzZS1jYWxsYmFjay1yZWYudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHVzZUNhbGxiYWNrUmVmKGNhbGxiYWNrKSB7XG4gIGNvbnN0IGNhbGxiYWNrUmVmID0gUmVhY3QudXNlUmVmKGNhbGxiYWNrKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjYWxsYmFja1JlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIH0pO1xuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gY2FsbGJhY2tSZWYuY3VycmVudD8uKC4uLmFyZ3MpLCBbXSk7XG59XG5leHBvcnQge1xuICB1c2VDYWxsYmFja1JlZlxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n      ref.current = callback;\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => ref.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lZmZlY3QtZXZlbnQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ29FO0FBQ3JDO0FBQy9CLDBCQUEwQix5TEFBSztBQUMvQiw4QkFBOEIseUxBQUs7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLHlDQUFZO0FBQzFCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxJQUFJO0FBQ0osSUFBSSxrRkFBZTtBQUNuQjtBQUNBLEtBQUs7QUFDTDtBQUNBLFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zLWd5bS1lbGl0ZS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWVmZmVjdC1ldmVudC9kaXN0L2luZGV4Lm1qcz84NzllIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy91c2UtZWZmZWN0LWV2ZW50LnRzeFxuaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdFwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlUmVhY3RFZmZlY3RFdmVudCA9IFJlYWN0W1wiIHVzZUVmZmVjdEV2ZW50IFwiLnRyaW0oKS50b1N0cmluZygpXTtcbnZhciB1c2VSZWFjdEluc2VydGlvbkVmZmVjdCA9IFJlYWN0W1wiIHVzZUluc2VydGlvbkVmZmVjdCBcIi50cmltKCkudG9TdHJpbmcoKV07XG5mdW5jdGlvbiB1c2VFZmZlY3RFdmVudChjYWxsYmFjaykge1xuICBpZiAodHlwZW9mIHVzZVJlYWN0RWZmZWN0RXZlbnQgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIHJldHVybiB1c2VSZWFjdEVmZmVjdEV2ZW50KGNhbGxiYWNrKTtcbiAgfVxuICBjb25zdCByZWYgPSBSZWFjdC51c2VSZWYoKCkgPT4ge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkNhbm5vdCBjYWxsIGFuIGV2ZW50IGhhbmRsZXIgd2hpbGUgcmVuZGVyaW5nLlwiKTtcbiAgfSk7XG4gIGlmICh0eXBlb2YgdXNlUmVhY3RJbnNlcnRpb25FZmZlY3QgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIHVzZVJlYWN0SW5zZXJ0aW9uRWZmZWN0KCgpID0+IHtcbiAgICAgIHJlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gICAgfSk7XG4gIH0gZWxzZSB7XG4gICAgdXNlTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICAgIHJlZi5jdXJyZW50ID0gY2FsbGJhY2s7XG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IHJlZi5jdXJyZW50Py4oLi4uYXJncyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUVmZmVjdEV2ZW50XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1lc2NhcGUta2V5ZG93bi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUNtQztBQUNsRTtBQUNBLDBCQUEwQixnRkFBYztBQUN4QyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsZUFBZTtBQUM5RSwrRUFBK0UsZUFBZTtBQUM5RixHQUFHO0FBQ0g7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zLWd5bS1lbGl0ZS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWVzY2FwZS1rZXlkb3duL2Rpc3QvaW5kZXgubWpzPzg5YjMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWVzY2FwZS1rZXlkb3duL3NyYy91c2UtZXNjYXBlLWtleWRvd24udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrUmVmIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmXCI7XG5mdW5jdGlvbiB1c2VFc2NhcGVLZXlkb3duKG9uRXNjYXBlS2V5RG93blByb3AsIG93bmVyRG9jdW1lbnQgPSBnbG9iYWxUaGlzPy5kb2N1bWVudCkge1xuICBjb25zdCBvbkVzY2FwZUtleURvd24gPSB1c2VDYWxsYmFja1JlZihvbkVzY2FwZUtleURvd25Qcm9wKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50KSA9PiB7XG4gICAgICBpZiAoZXZlbnQua2V5ID09PSBcIkVzY2FwZVwiKSB7XG4gICAgICAgIG9uRXNjYXBlS2V5RG93bihldmVudCk7XG4gICAgICB9XG4gICAgfTtcbiAgICBvd25lckRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24sIHsgY2FwdHVyZTogdHJ1ZSB9KTtcbiAgICByZXR1cm4gKCkgPT4gb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKFwia2V5ZG93blwiLCBoYW5kbGVLZXlEb3duLCB7IGNhcHR1cmU6IHRydWUgfSk7XG4gIH0sIFtvbkVzY2FwZUtleURvd24sIG93bmVyRG9jdW1lbnRdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUVzY2FwZUtleWRvd25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsOENBQThDLGtEQUFxQjtBQUNuRTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wb3MtZ3ltLWVsaXRlLy4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcz9lMWVlIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1sYXlvdXQtZWZmZWN0L3NyYy91c2UtbGF5b3V0LWVmZmVjdC50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xudmFyIHVzZUxheW91dEVmZmVjdDIgPSBnbG9iYWxUaGlzPy5kb2N1bWVudCA/IFJlYWN0LnVzZUxheW91dEVmZmVjdCA6ICgpID0+IHtcbn07XG5leHBvcnQge1xuICB1c2VMYXlvdXRFZmZlY3QyIGFzIHVzZUxheW91dEVmZmVjdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXZpc3VhbGx5LWhpZGRlbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUMrQjtBQUN1QjtBQUNkO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLHFCQUFxQiw2Q0FBZ0I7QUFDckM7QUFDQSwyQkFBMkIsc0RBQUc7QUFDOUIsTUFBTSxnRUFBUztBQUNmO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFLRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcG9zLWd5bS1lbGl0ZS8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdmlzdWFsbHktaGlkZGVuL2Rpc3QvaW5kZXgubWpzP2ZmODQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3Zpc3VhbGx5LWhpZGRlbi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgUHJpbWl0aXZlIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1wcmltaXRpdmVcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIFZJU1VBTExZX0hJRERFTl9TVFlMRVMgPSBPYmplY3QuZnJlZXplKHtcbiAgLy8gU2VlOiBodHRwczovL2dpdGh1Yi5jb20vdHdicy9ib290c3RyYXAvYmxvYi9tYWluL3Njc3MvbWl4aW5zL192aXN1YWxseS1oaWRkZW4uc2Nzc1xuICBwb3NpdGlvbjogXCJhYnNvbHV0ZVwiLFxuICBib3JkZXI6IDAsXG4gIHdpZHRoOiAxLFxuICBoZWlnaHQ6IDEsXG4gIHBhZGRpbmc6IDAsXG4gIG1hcmdpbjogLTEsXG4gIG92ZXJmbG93OiBcImhpZGRlblwiLFxuICBjbGlwOiBcInJlY3QoMCwgMCwgMCwgMClcIixcbiAgd2hpdGVTcGFjZTogXCJub3dyYXBcIixcbiAgd29yZFdyYXA6IFwibm9ybWFsXCJcbn0pO1xudmFyIE5BTUUgPSBcIlZpc3VhbGx5SGlkZGVuXCI7XG52YXIgVmlzdWFsbHlIaWRkZW4gPSBSZWFjdC5mb3J3YXJkUmVmKFxuICAocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFxuICAgICAgUHJpbWl0aXZlLnNwYW4sXG4gICAgICB7XG4gICAgICAgIC4uLnByb3BzLFxuICAgICAgICByZWY6IGZvcndhcmRlZFJlZixcbiAgICAgICAgc3R5bGU6IHsgLi4uVklTVUFMTFlfSElEREVOX1NUWUxFUywgLi4ucHJvcHMuc3R5bGUgfVxuICAgICAgfVxuICAgICk7XG4gIH1cbik7XG5WaXN1YWxseUhpZGRlbi5kaXNwbGF5TmFtZSA9IE5BTUU7XG52YXIgUm9vdCA9IFZpc3VhbGx5SGlkZGVuO1xuZXhwb3J0IHtcbiAgUm9vdCxcbiAgVklTVUFMTFlfSElEREVOX1NUWUxFUyxcbiAgVmlzdWFsbHlIaWRkZW5cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;