"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"87d253316fe2\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YmJmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjg3ZDI1MzMxNmZlMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: function() { return /* binding */ Providers; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useLanguage: function() { return /* binding */ useLanguage; },\n/* harmony export */   useTheme: function() { return /* binding */ useTheme; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,useTheme,useLanguage,Providers auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    signOut: async ()=>{}\n});\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    theme: \"light\",\n    toggleTheme: ()=>{}\n});\nconst useTheme = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n_s1(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    language: \"en\",\n    setLanguage: ()=>{},\n    t: (key)=>key\n});\nconst useLanguage = ()=>{\n    _s2();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n_s2(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Simple translation function (you can replace with a more robust solution)\nconst translations = {\n    en: {\n        // Navigation\n        dashboard: \"Dashboard\",\n        members: \"Members\",\n        pos: \"Point of Sale\",\n        inventory: \"Inventory\",\n        sports: \"Sports\",\n        classes: \"Classes\",\n        reports: \"Reports\",\n        settings: \"Settings\",\n        logout: \"Logout\",\n        // Member Management\n        \"members_management\": \"Members Management\",\n        \"manage_gym_members\": \"Manage gym members and their subscriptions\",\n        \"add_new_member\": \"Add New Member\",\n        \"edit_member\": \"Edit Member\",\n        \"member_details\": \"Member Details\",\n        \"subscription_details\": \"Subscription Details\",\n        \"personal_information\": \"Personal Information\",\n        \"contact_information\": \"Contact Information\",\n        \"full_name\": \"Full Name\",\n        \"gender\": \"Gender\",\n        \"age\": \"Age\",\n        \"phone\": \"Phone\",\n        \"email\": \"Email\",\n        \"male\": \"Male\",\n        \"female\": \"Female\",\n        \"pregnant\": \"Pregnant\",\n        \"situation\": \"Situation\",\n        \"remarks\": \"Remarks\",\n        \"sport\": \"Sport\",\n        \"plan_type\": \"Plan Type\",\n        \"monthly\": \"Monthly\",\n        \"quarterly\": \"Quarterly\",\n        \"yearly\": \"Yearly\",\n        \"price\": \"Price\",\n        \"total_amount\": \"Total Amount\",\n        \"save\": \"Save\",\n        \"cancel\": \"Cancel\",\n        \"edit\": \"Edit\",\n        \"delete\": \"Delete\",\n        \"renew\": \"Renew\",\n        \"active\": \"Active\",\n        \"expiring\": \"Expiring\",\n        \"expired\": \"Expired\",\n        \"all\": \"All\",\n        \"search_members\": \"Search members...\",\n        \"filter_by_status\": \"Filter by Status\",\n        \"export_csv\": \"Export CSV\",\n        \"export_excel\": \"Export Excel\",\n        \"bulk_operations\": \"Bulk Operations\",\n        \"select_all\": \"Select All\",\n        \"selected_count\": \"Selected\",\n        \"delete_selected\": \"Delete Selected\",\n        \"update_status\": \"Update Status\",\n        \"no_members_found\": \"No members found\",\n        \"try_adjusting_search\": \"Try adjusting your search or filters\",\n        \"get_started_adding\": \"Get started by adding your first member\",\n        \"member_added\": \"Member Added\",\n        \"member_updated\": \"Member Updated\",\n        \"member_deleted\": \"Member Deleted\",\n        \"subscription_renewed\": \"Subscription Renewed\",\n        \"add_new_sport\": \"Add New Sport\",\n        \"sport_categories\": \"Sport Categories\",\n        // Sports Categories\n        \"gym_strength_training\": \"Gym & Strength Training\",\n        \"cardio_endurance\": \"Cardio & Endurance\",\n        \"boxing_martial_arts\": \"Boxing & Martial Arts\",\n        \"group_classes\": \"Group Classes\",\n        \"mind_body\": \"Mind & Body\",\n        \"sports_training\": \"Sports Training\",\n        \"rehabilitation_recovery\": \"Rehabilitation & Recovery\",\n        \"personal_training\": \"Personal Training Programs\",\n        // Individual Sports\n        \"bodybuilding\": \"Bodybuilding\",\n        \"weightlifting\": \"Weightlifting\",\n        \"powerlifting\": \"Powerlifting\",\n        \"crossfit\": \"CrossFit\",\n        \"functional_training\": \"Functional Training\",\n        \"calisthenics\": \"Calisthenics\",\n        \"treadmill_running\": \"Treadmill Running\",\n        \"cycling_spinning\": \"Cycling/Spinning\",\n        \"hiit\": \"HIIT\",\n        \"stair_climber\": \"Stair Climber\",\n        \"rowing_machine\": \"Rowing Machine\",\n        \"boxing\": \"Boxing\",\n        \"kickboxing\": \"Kickboxing\",\n        \"mma\": \"MMA\",\n        \"muay_thai\": \"Muay Thai\",\n        \"brazilian_jiu_jitsu\": \"Brazilian Jiu-Jitsu\",\n        \"taekwondo\": \"Taekwondo\",\n        \"karate\": \"Karate\",\n        \"zumba\": \"Zumba\",\n        \"aerobics\": \"Aerobics\",\n        \"step\": \"Step\",\n        \"dance_fitness\": \"Dance Fitness\",\n        \"bodypump\": \"BodyPump\",\n        \"bootcamp\": \"Bootcamp\",\n        \"yoga\": \"Yoga\",\n        \"pilates\": \"Pilates\",\n        \"stretching\": \"Stretching\",\n        \"meditation\": \"Meditation\",\n        \"breathing_exercises\": \"Breathing Exercises\",\n        \"football_conditioning\": \"Football Conditioning\",\n        \"basketball_drills\": \"Basketball Drills\",\n        \"athletic_performance\": \"Athletic Performance\",\n        \"speed_agility_training\": \"Speed & Agility Training\",\n        \"core_strengthening\": \"Core Strengthening\",\n        \"physiotherapy\": \"Physiotherapy\",\n        \"foam_rolling\": \"Foam Rolling\",\n        \"mobility_training\": \"Mobility Training\",\n        \"post_injury_recovery\": \"Post-injury Recovery\",\n        \"massage_therapy\": \"Massage Therapy\",\n        \"weight_loss_plan\": \"Weight Loss Plan\",\n        \"muscle_gain_program\": \"Muscle Gain Program\",\n        \"strength_building\": \"Strength Building\",\n        \"senior_fitness\": \"Senior Fitness\",\n        \"pre_post_natal_fitness\": \"Pre/Post-natal Fitness\"\n    },\n    fr: {\n        // Navigation\n        dashboard: \"Tableau de bord\",\n        members: \"Membres\",\n        pos: \"Point de vente\",\n        inventory: \"Inventaire\",\n        sports: \"Sports\",\n        classes: \"Cours\",\n        reports: \"Rapports\",\n        settings: \"Param\\xe8tres\",\n        logout: \"D\\xe9connexion\",\n        // Member Management\n        \"members_management\": \"Gestion des Membres\",\n        \"manage_gym_members\": \"G\\xe9rer les membres de la salle et leurs abonnements\",\n        \"add_new_member\": \"Ajouter un Nouveau Membre\",\n        \"edit_member\": \"Modifier le Membre\",\n        \"member_details\": \"D\\xe9tails du Membre\",\n        \"subscription_details\": \"D\\xe9tails de l'Abonnement\",\n        \"personal_information\": \"Informations Personnelles\",\n        \"contact_information\": \"Informations de Contact\",\n        \"full_name\": \"Nom Complet\",\n        \"gender\": \"Sexe\",\n        \"age\": \"\\xc2ge\",\n        \"phone\": \"T\\xe9l\\xe9phone\",\n        \"email\": \"Email\",\n        \"male\": \"Homme\",\n        \"female\": \"Femme\",\n        \"pregnant\": \"Enceinte\",\n        \"situation\": \"Situation\",\n        \"remarks\": \"Remarques\",\n        \"sport\": \"Sport\",\n        \"plan_type\": \"Type d'Abonnement\",\n        \"monthly\": \"Mensuel\",\n        \"quarterly\": \"Trimestriel\",\n        \"yearly\": \"Annuel\",\n        \"price\": \"Prix\",\n        \"total_amount\": \"Montant Total\",\n        \"save\": \"Enregistrer\",\n        \"cancel\": \"Annuler\",\n        \"edit\": \"Modifier\",\n        \"delete\": \"Supprimer\",\n        \"renew\": \"Renouveler\",\n        \"active\": \"Actif\",\n        \"expiring\": \"Expire Bient\\xf4t\",\n        \"expired\": \"Expir\\xe9\",\n        \"all\": \"Tous\",\n        \"search_members\": \"Rechercher des membres...\",\n        \"filter_by_status\": \"Filtrer par Statut\",\n        \"export_csv\": \"Exporter CSV\",\n        \"export_excel\": \"Exporter Excel\",\n        \"bulk_operations\": \"Op\\xe9rations en Lot\",\n        \"select_all\": \"Tout S\\xe9lectionner\",\n        \"selected_count\": \"S\\xe9lectionn\\xe9s\",\n        \"delete_selected\": \"Supprimer S\\xe9lectionn\\xe9s\",\n        \"update_status\": \"Mettre \\xe0 Jour le Statut\",\n        \"no_members_found\": \"Aucun membre trouv\\xe9\",\n        \"try_adjusting_search\": \"Essayez d'ajuster votre recherche ou vos filtres\",\n        \"get_started_adding\": \"Commencez par ajouter votre premier membre\",\n        \"member_added\": \"Membre Ajout\\xe9\",\n        \"member_updated\": \"Membre Mis \\xe0 Jour\",\n        \"member_deleted\": \"Membre Supprim\\xe9\",\n        \"subscription_renewed\": \"Abonnement Renouvel\\xe9\",\n        \"add_new_sport\": \"Ajouter un Nouveau Sport\",\n        \"sport_categories\": \"Cat\\xe9gories de Sports\",\n        // Sports Categories\n        \"gym_strength_training\": \"Musculation et Force\",\n        \"cardio_endurance\": \"Cardio et Endurance\",\n        \"boxing_martial_arts\": \"Boxe et Arts Martiaux\",\n        \"group_classes\": \"Cours Collectifs\",\n        \"mind_body\": \"Corps et Esprit\",\n        \"sports_training\": \"Entra\\xeenement Sportif\",\n        \"rehabilitation_recovery\": \"R\\xe9\\xe9ducation et R\\xe9cup\\xe9ration\",\n        \"personal_training\": \"Programmes d'Entra\\xeenement Personnel\",\n        // Individual Sports\n        \"bodybuilding\": \"Bodybuilding\",\n        \"weightlifting\": \"Halt\\xe9rophilie\",\n        \"powerlifting\": \"Force Athl\\xe9tique\",\n        \"crossfit\": \"CrossFit\",\n        \"functional_training\": \"Entra\\xeenement Fonctionnel\",\n        \"calisthenics\": \"Callisth\\xe9nie\",\n        \"treadmill_running\": \"Course sur Tapis\",\n        \"cycling_spinning\": \"Cyclisme/Spinning\",\n        \"hiit\": \"HIIT\",\n        \"stair_climber\": \"Escalier\",\n        \"rowing_machine\": \"Rameur\",\n        \"boxing\": \"Boxe\",\n        \"kickboxing\": \"Kickboxing\",\n        \"mma\": \"MMA\",\n        \"muay_thai\": \"Muay Thai\",\n        \"brazilian_jiu_jitsu\": \"Jiu-Jitsu Br\\xe9silien\",\n        \"taekwondo\": \"Taekwondo\",\n        \"karate\": \"Karat\\xe9\",\n        \"zumba\": \"Zumba\",\n        \"aerobics\": \"A\\xe9robic\",\n        \"step\": \"Step\",\n        \"dance_fitness\": \"Fitness Danse\",\n        \"bodypump\": \"BodyPump\",\n        \"bootcamp\": \"Bootcamp\",\n        \"yoga\": \"Yoga\",\n        \"pilates\": \"Pilates\",\n        \"stretching\": \"\\xc9tirements\",\n        \"meditation\": \"M\\xe9ditation\",\n        \"breathing_exercises\": \"Exercices de Respiration\",\n        \"football_conditioning\": \"Conditionnement Football\",\n        \"basketball_drills\": \"Exercices Basketball\",\n        \"athletic_performance\": \"Performance Athl\\xe9tique\",\n        \"speed_agility_training\": \"Entra\\xeenement Vitesse et Agilit\\xe9\",\n        \"core_strengthening\": \"Renforcement du Tronc\",\n        \"physiotherapy\": \"Physioth\\xe9rapie\",\n        \"foam_rolling\": \"Rouleau de Massage\",\n        \"mobility_training\": \"Entra\\xeenement de Mobilit\\xe9\",\n        \"post_injury_recovery\": \"R\\xe9cup\\xe9ration Post-Blessure\",\n        \"massage_therapy\": \"Th\\xe9rapie par Massage\",\n        \"weight_loss_plan\": \"Programme de Perte de Poids\",\n        \"muscle_gain_program\": \"Programme de Prise de Muscle\",\n        \"strength_building\": \"D\\xe9veloppement de la Force\",\n        \"senior_fitness\": \"Fitness Senior\",\n        \"pre_post_natal_fitness\": \"Fitness Pr\\xe9/Post-natal\"\n    },\n    ar: {\n        // Navigation\n        dashboard: \"لوحة التحكم\",\n        members: \"الأعضاء\",\n        pos: \"نقطة البيع\",\n        inventory: \"المخزون\",\n        sports: \"الرياضات\",\n        classes: \"الحصص\",\n        reports: \"التقارير\",\n        settings: \"الإعدادات\",\n        logout: \"تسجيل الخروج\",\n        // Member Management\n        \"members_management\": \"إدارة الأعضاء\",\n        \"manage_gym_members\": \"إدارة أعضاء النادي واشتراكاتهم\",\n        \"add_new_member\": \"إضافة عضو جديد\",\n        \"edit_member\": \"تعديل العضو\",\n        \"member_details\": \"تفاصيل العضو\",\n        \"subscription_details\": \"تفاصيل الاشتراك\",\n        \"personal_information\": \"المعلومات الشخصية\",\n        \"contact_information\": \"معلومات الاتصال\",\n        \"full_name\": \"الاسم الكامل\",\n        \"gender\": \"الجنس\",\n        \"age\": \"العمر\",\n        \"phone\": \"الهاتف\",\n        \"email\": \"البريد الإلكتروني\",\n        \"male\": \"ذكر\",\n        \"female\": \"أنثى\",\n        \"pregnant\": \"حامل\",\n        \"situation\": \"الحالة\",\n        \"remarks\": \"ملاحظات\",\n        \"sport\": \"الرياضة\",\n        \"plan_type\": \"نوع الاشتراك\",\n        \"monthly\": \"شهري\",\n        \"quarterly\": \"ربع سنوي\",\n        \"yearly\": \"سنوي\",\n        \"price\": \"السعر\",\n        \"total_amount\": \"المبلغ الإجمالي\",\n        \"save\": \"حفظ\",\n        \"cancel\": \"إلغاء\",\n        \"edit\": \"تعديل\",\n        \"delete\": \"حذف\",\n        \"renew\": \"تجديد\",\n        \"active\": \"نشط\",\n        \"expiring\": \"ينتهي قريباً\",\n        \"expired\": \"منتهي الصلاحية\",\n        \"all\": \"الكل\",\n        \"search_members\": \"البحث عن الأعضاء...\",\n        \"filter_by_status\": \"تصفية حسب الحالة\",\n        \"export_csv\": \"تصدير CSV\",\n        \"export_excel\": \"تصدير Excel\",\n        \"bulk_operations\": \"العمليات المجمعة\",\n        \"select_all\": \"تحديد الكل\",\n        \"selected_count\": \"محدد\",\n        \"delete_selected\": \"حذف المحدد\",\n        \"update_status\": \"تحديث الحالة\",\n        \"no_members_found\": \"لم يتم العثور على أعضاء\",\n        \"try_adjusting_search\": \"حاول تعديل البحث أو المرشحات\",\n        \"get_started_adding\": \"ابدأ بإضافة عضوك الأول\",\n        \"member_added\": \"تم إضافة العضو\",\n        \"member_updated\": \"تم تحديث العضو\",\n        \"member_deleted\": \"تم حذف العضو\",\n        \"subscription_renewed\": \"تم تجديد الاشتراك\",\n        \"add_new_sport\": \"إضافة رياضة جديدة\",\n        \"sport_categories\": \"فئات الرياضة\",\n        // Sports Categories\n        \"gym_strength_training\": \"النادي وتدريب القوة\",\n        \"cardio_endurance\": \"الكارديو والتحمل\",\n        \"boxing_martial_arts\": \"الملاكمة والفنون القتالية\",\n        \"group_classes\": \"الحصص الجماعية\",\n        \"mind_body\": \"العقل والجسم\",\n        \"sports_training\": \"التدريب الرياضي\",\n        \"rehabilitation_recovery\": \"التأهيل والاستشفاء\",\n        \"personal_training\": \"برامج التدريب الشخصي\",\n        // Individual Sports\n        \"bodybuilding\": \"كمال الأجسام\",\n        \"weightlifting\": \"رفع الأثقال\",\n        \"powerlifting\": \"رفع القوة\",\n        \"crossfit\": \"كروس فيت\",\n        \"functional_training\": \"التدريب الوظيفي\",\n        \"calisthenics\": \"التمارين البدنية\",\n        \"treadmill_running\": \"الجري على المشاية\",\n        \"cycling_spinning\": \"ركوب الدراجات/السبينينغ\",\n        \"hiit\": \"التدريب المتقطع عالي الكثافة\",\n        \"stair_climber\": \"تسلق الدرج\",\n        \"rowing_machine\": \"آلة التجديف\",\n        \"boxing\": \"الملاكمة\",\n        \"kickboxing\": \"الكيك بوكسينغ\",\n        \"mma\": \"الفنون القتالية المختلطة\",\n        \"muay_thai\": \"مواي تاي\",\n        \"brazilian_jiu_jitsu\": \"الجوجيتسو البرازيلي\",\n        \"taekwondo\": \"التايكوندو\",\n        \"karate\": \"الكاراتيه\",\n        \"zumba\": \"الزومبا\",\n        \"aerobics\": \"الأيروبيك\",\n        \"step\": \"الستيب\",\n        \"dance_fitness\": \"الرقص الرياضي\",\n        \"bodypump\": \"بودي بامب\",\n        \"bootcamp\": \"المعسكر التدريبي\",\n        \"yoga\": \"اليوغا\",\n        \"pilates\": \"البيلاتس\",\n        \"stretching\": \"التمدد\",\n        \"meditation\": \"التأمل\",\n        \"breathing_exercises\": \"تمارين التنفس\",\n        \"football_conditioning\": \"تكييف كرة القدم\",\n        \"basketball_drills\": \"تدريبات كرة السلة\",\n        \"athletic_performance\": \"الأداء الرياضي\",\n        \"speed_agility_training\": \"تدريب السرعة والرشاقة\",\n        \"core_strengthening\": \"تقوية الجذع\",\n        \"physiotherapy\": \"العلاج الطبيعي\",\n        \"foam_rolling\": \"التدليك بالأسطوانة\",\n        \"mobility_training\": \"تدريب الحركة\",\n        \"post_injury_recovery\": \"التعافي بعد الإصابة\",\n        \"massage_therapy\": \"العلاج بالتدليك\",\n        \"weight_loss_plan\": \"برنامج إنقاص الوزن\",\n        \"muscle_gain_program\": \"برنامج زيادة العضلات\",\n        \"strength_building\": \"بناء القوة\",\n        \"senior_fitness\": \"لياقة كبار السن\",\n        \"pre_post_natal_fitness\": \"لياقة ما قبل/بعد الولادة\"\n    }\n};\nfunction Providers(param) {\n    let { children } = param;\n    _s3();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simple localStorage-based auth check\n        const checkAuth = ()=>{\n            try {\n                const authStatus = localStorage.getItem(\"gym-auth-status\");\n                const demoUser = localStorage.getItem(\"gym-demo-user\");\n                if (authStatus === \"authenticated\" && demoUser) {\n                    setUser(JSON.parse(demoUser));\n                } else {\n                    // Auto-set demo authentication for development\n                    const demoDemoUser = {\n                        id: \"demo-user\",\n                        email: \"<EMAIL>\",\n                        user_metadata: {\n                            full_name: \"Demo Admin\"\n                        }\n                    };\n                    localStorage.setItem(\"gym-demo-user\", JSON.stringify(demoDemoUser));\n                    localStorage.setItem(\"gym-auth-status\", \"authenticated\");\n                    setUser(demoDemoUser);\n                }\n            } catch (error) {\n                console.log(\"Error checking auth:\", error);\n                // Even on error, set demo user for development\n                const demoDemoUser = {\n                    id: \"demo-user\",\n                    email: \"<EMAIL>\",\n                    user_metadata: {\n                        full_name: \"Demo Admin\"\n                    }\n                };\n                localStorage.setItem(\"gym-demo-user\", JSON.stringify(demoDemoUser));\n                localStorage.setItem(\"gym-auth-status\", \"authenticated\");\n                setUser(demoDemoUser);\n            } finally{\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load theme from localStorage\n        const savedTheme = localStorage.getItem(\"gym-theme\");\n        if (savedTheme) {\n            setTheme(savedTheme);\n            document.documentElement.classList.toggle(\"dark\", savedTheme === \"dark\");\n        }\n        // Load language from localStorage\n        const savedLanguage = localStorage.getItem(\"gym-language\");\n        if (savedLanguage) {\n            setLanguage(savedLanguage);\n            document.documentElement.setAttribute(\"lang\", savedLanguage);\n            document.documentElement.setAttribute(\"dir\", savedLanguage === \"ar\" ? \"rtl\" : \"ltr\");\n        }\n    }, []);\n    const signOut = async ()=>{\n        // Clear all auth data\n        localStorage.removeItem(\"gym-demo-user\");\n        localStorage.removeItem(\"gym-auth-status\");\n        setUser(null);\n        // Redirect to login\n        window.location.href = \"/login\";\n    };\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        localStorage.setItem(\"gym-theme\", newTheme);\n        document.documentElement.classList.toggle(\"dark\", newTheme === \"dark\");\n    };\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        localStorage.setItem(\"gym-language\", lang);\n        document.documentElement.setAttribute(\"lang\", lang);\n        document.documentElement.setAttribute(\"dir\", lang === \"ar\" ? \"rtl\" : \"ltr\");\n    };\n    const t = (key)=>{\n        var _translations_language;\n        return ((_translations_language = translations[language]) === null || _translations_language === void 0 ? void 0 : _translations_language[key]) || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading,\n            signOut\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n            value: {\n                theme,\n                toggleTheme\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n                value: {\n                    language,\n                    setLanguage: handleSetLanguage,\n                    t\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n                lineNumber: 528,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 527,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 526,\n        columnNumber: 5\n    }, this);\n}\n_s3(Providers, \"zVHADGN2gQsHwcsj+EW/UQDHhXo=\");\n_c = Providers;\nvar _c;\n$RefreshReg$(_c, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers.tsx\n"));

/***/ })

});