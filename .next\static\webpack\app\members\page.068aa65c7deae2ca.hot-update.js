"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/members/page",{

/***/ "(app-pages-browser)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: function() { return /* binding */ Providers; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   useLanguage: function() { return /* binding */ useLanguage; },\n/* harmony export */   useTheme: function() { return /* binding */ useTheme; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,useTheme,useLanguage,Providers auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    signOut: async ()=>{}\n});\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    theme: \"light\",\n    toggleTheme: ()=>{}\n});\nconst useTheme = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n};\n_s1(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    language: \"en\",\n    setLanguage: ()=>{},\n    t: (key)=>key\n});\nconst useLanguage = ()=>{\n    _s2();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (!context) {\n        throw new Error(\"useLanguage must be used within a LanguageProvider\");\n    }\n    return context;\n};\n_s2(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Simple translation function (you can replace with a more robust solution)\nconst translations = {\n    en: {\n        // Navigation\n        dashboard: \"Dashboard\",\n        members: \"Members\",\n        pos: \"Point of Sale\",\n        inventory: \"Inventory\",\n        sports: \"Sports\",\n        classes: \"Classes\",\n        reports: \"Reports\",\n        settings: \"Settings\",\n        logout: \"Logout\",\n        // Member Management\n        \"members_management\": \"Members Management\",\n        \"manage_gym_members\": \"Manage gym members and their subscriptions\",\n        \"add_new_member\": \"Add New Member\",\n        \"edit_member\": \"Edit Member\",\n        \"member_details\": \"Member Details\",\n        \"subscription_details\": \"Subscription Details\",\n        \"personal_information\": \"Personal Information\",\n        \"contact_information\": \"Contact Information\",\n        \"full_name\": \"Full Name\",\n        \"gender\": \"Gender\",\n        \"age\": \"Age\",\n        \"phone\": \"Phone\",\n        \"email\": \"Email\",\n        \"male\": \"Male\",\n        \"female\": \"Female\",\n        \"pregnant\": \"Pregnant\",\n        \"situation\": \"Situation\",\n        \"remarks\": \"Remarks\",\n        \"sport\": \"Sport\",\n        \"plan_type\": \"Plan Type\",\n        \"monthly\": \"Monthly\",\n        \"quarterly\": \"Quarterly\",\n        \"yearly\": \"Yearly\",\n        \"price\": \"Price\",\n        \"total_amount\": \"Total Amount\",\n        \"save\": \"Save\",\n        \"cancel\": \"Cancel\",\n        \"edit\": \"Edit\",\n        \"delete\": \"Delete\",\n        \"renew\": \"Renew\",\n        \"active\": \"Active\",\n        \"expiring\": \"Expiring\",\n        \"expired\": \"Expired\",\n        \"all\": \"All\",\n        \"search_members\": \"Search members...\",\n        \"filter_by_status\": \"Filter by Status\",\n        \"export_csv\": \"Export CSV\",\n        \"export_excel\": \"Export Excel\",\n        \"bulk_operations\": \"Bulk Operations\",\n        \"select_all\": \"Select All\",\n        \"selected_count\": \"Selected\",\n        \"delete_selected\": \"Delete Selected\",\n        \"update_status\": \"Update Status\",\n        \"no_members_found\": \"No members found\",\n        \"try_adjusting_search\": \"Try adjusting your search or filters\",\n        \"get_started_adding\": \"Get started by adding your first member\",\n        \"member_added\": \"Member Added\",\n        \"member_updated\": \"Member Updated\",\n        \"member_deleted\": \"Member Deleted\",\n        \"subscription_renewed\": \"Subscription Renewed\",\n        \"add_new_sport\": \"Add New Sport\",\n        \"sport_categories\": \"Sport Categories\",\n        // Sports Categories\n        \"gym_strength_training\": \"Gym & Strength Training\",\n        \"cardio_endurance\": \"Cardio & Endurance\",\n        \"boxing_martial_arts\": \"Boxing & Martial Arts\",\n        \"group_classes\": \"Group Classes\",\n        \"mind_body\": \"Mind & Body\",\n        \"sports_training\": \"Sports Training\",\n        \"rehabilitation_recovery\": \"Rehabilitation & Recovery\",\n        \"personal_training\": \"Personal Training Programs\",\n        // Individual Sports\n        \"bodybuilding\": \"Bodybuilding\",\n        \"weightlifting\": \"Weightlifting\",\n        \"powerlifting\": \"Powerlifting\",\n        \"crossfit\": \"CrossFit\",\n        \"functional_training\": \"Functional Training\",\n        \"calisthenics\": \"Calisthenics\",\n        \"treadmill_running\": \"Treadmill Running\",\n        \"cycling_spinning\": \"Cycling/Spinning\",\n        \"hiit\": \"HIIT\",\n        \"stair_climber\": \"Stair Climber\",\n        \"rowing_machine\": \"Rowing Machine\",\n        \"boxing\": \"Boxing\",\n        \"kickboxing\": \"Kickboxing\",\n        \"mma\": \"MMA\",\n        \"muay_thai\": \"Muay Thai\",\n        \"brazilian_jiu_jitsu\": \"Brazilian Jiu-Jitsu\",\n        \"taekwondo\": \"Taekwondo\",\n        \"karate\": \"Karate\",\n        \"zumba\": \"Zumba\",\n        \"aerobics\": \"Aerobics\",\n        \"step\": \"Step\",\n        \"dance_fitness\": \"Dance Fitness\",\n        \"bodypump\": \"BodyPump\",\n        \"bootcamp\": \"Bootcamp\",\n        \"yoga\": \"Yoga\",\n        \"pilates\": \"Pilates\",\n        \"stretching\": \"Stretching\",\n        \"meditation\": \"Meditation\",\n        \"breathing_exercises\": \"Breathing Exercises\",\n        \"football_conditioning\": \"Football Conditioning\",\n        \"basketball_drills\": \"Basketball Drills\",\n        \"athletic_performance\": \"Athletic Performance\",\n        \"speed_agility_training\": \"Speed & Agility Training\",\n        \"core_strengthening\": \"Core Strengthening\",\n        \"physiotherapy\": \"Physiotherapy\",\n        \"foam_rolling\": \"Foam Rolling\",\n        \"mobility_training\": \"Mobility Training\",\n        \"post_injury_recovery\": \"Post-injury Recovery\",\n        \"massage_therapy\": \"Massage Therapy\",\n        \"weight_loss_plan\": \"Weight Loss Plan\",\n        \"muscle_gain_program\": \"Muscle Gain Program\",\n        \"strength_building\": \"Strength Building\",\n        \"senior_fitness\": \"Senior Fitness\",\n        \"pre_post_natal_fitness\": \"Pre/Post-natal Fitness\"\n    },\n    fr: {\n        // Navigation\n        dashboard: \"Tableau de bord\",\n        members: \"Membres\",\n        pos: \"Point de vente\",\n        inventory: \"Inventaire\",\n        sports: \"Sports\",\n        classes: \"Cours\",\n        reports: \"Rapports\",\n        settings: \"Param\\xe8tres\",\n        logout: \"D\\xe9connexion\",\n        // Member Management\n        \"members_management\": \"Gestion des Membres\",\n        \"manage_gym_members\": \"G\\xe9rer les membres de la salle et leurs abonnements\",\n        \"add_new_member\": \"Ajouter un Nouveau Membre\",\n        \"edit_member\": \"Modifier le Membre\",\n        \"member_details\": \"D\\xe9tails du Membre\",\n        \"subscription_details\": \"D\\xe9tails de l'Abonnement\",\n        \"personal_information\": \"Informations Personnelles\",\n        \"contact_information\": \"Informations de Contact\",\n        \"full_name\": \"Nom Complet\",\n        \"gender\": \"Sexe\",\n        \"age\": \"\\xc2ge\",\n        \"phone\": \"T\\xe9l\\xe9phone\",\n        \"email\": \"Email\",\n        \"male\": \"Homme\",\n        \"female\": \"Femme\",\n        \"pregnant\": \"Enceinte\",\n        \"situation\": \"Situation\",\n        \"remarks\": \"Remarques\",\n        \"sport\": \"Sport\",\n        \"plan_type\": \"Type d'Abonnement\",\n        \"monthly\": \"Mensuel\",\n        \"quarterly\": \"Trimestriel\",\n        \"yearly\": \"Annuel\",\n        \"price\": \"Prix\",\n        \"total_amount\": \"Montant Total\",\n        \"save\": \"Enregistrer\",\n        \"cancel\": \"Annuler\",\n        \"edit\": \"Modifier\",\n        \"delete\": \"Supprimer\",\n        \"renew\": \"Renouveler\",\n        \"active\": \"Actif\",\n        \"expiring\": \"Expire Bient\\xf4t\",\n        \"expired\": \"Expir\\xe9\",\n        \"all\": \"Tous\",\n        \"search_members\": \"Rechercher des membres...\",\n        \"filter_by_status\": \"Filtrer par Statut\",\n        \"export_csv\": \"Exporter CSV\",\n        \"export_excel\": \"Exporter Excel\",\n        \"bulk_operations\": \"Op\\xe9rations en Lot\",\n        \"select_all\": \"Tout S\\xe9lectionner\",\n        \"selected_count\": \"S\\xe9lectionn\\xe9s\",\n        \"delete_selected\": \"Supprimer S\\xe9lectionn\\xe9s\",\n        \"update_status\": \"Mettre \\xe0 Jour le Statut\",\n        \"no_members_found\": \"Aucun membre trouv\\xe9\",\n        \"try_adjusting_search\": \"Essayez d'ajuster votre recherche ou vos filtres\",\n        \"get_started_adding\": \"Commencez par ajouter votre premier membre\",\n        \"member_added\": \"Membre Ajout\\xe9\",\n        \"member_updated\": \"Membre Mis \\xe0 Jour\",\n        \"member_deleted\": \"Membre Supprim\\xe9\",\n        \"subscription_renewed\": \"Abonnement Renouvel\\xe9\",\n        \"add_new_sport\": \"Ajouter un Nouveau Sport\",\n        \"sport_categories\": \"Cat\\xe9gories de Sports\",\n        // Sports Categories\n        \"gym_strength_training\": \"Musculation et Force\",\n        \"cardio_endurance\": \"Cardio et Endurance\",\n        \"boxing_martial_arts\": \"Boxe et Arts Martiaux\",\n        \"group_classes\": \"Cours Collectifs\",\n        \"mind_body\": \"Corps et Esprit\",\n        \"sports_training\": \"Entra\\xeenement Sportif\",\n        \"rehabilitation_recovery\": \"R\\xe9\\xe9ducation et R\\xe9cup\\xe9ration\",\n        \"personal_training\": \"Programmes d'Entra\\xeenement Personnel\",\n        // Individual Sports\n        \"bodybuilding\": \"Bodybuilding\",\n        \"weightlifting\": \"Halt\\xe9rophilie\",\n        \"powerlifting\": \"Force Athl\\xe9tique\",\n        \"crossfit\": \"CrossFit\",\n        \"functional_training\": \"Entra\\xeenement Fonctionnel\",\n        \"calisthenics\": \"Callisth\\xe9nie\",\n        \"treadmill_running\": \"Course sur Tapis\",\n        \"cycling_spinning\": \"Cyclisme/Spinning\",\n        \"hiit\": \"HIIT\",\n        \"stair_climber\": \"Escalier\",\n        \"rowing_machine\": \"Rameur\",\n        \"boxing\": \"Boxe\",\n        \"kickboxing\": \"Kickboxing\",\n        \"mma\": \"MMA\",\n        \"muay_thai\": \"Muay Thai\",\n        \"brazilian_jiu_jitsu\": \"Jiu-Jitsu Br\\xe9silien\",\n        \"taekwondo\": \"Taekwondo\",\n        \"karate\": \"Karat\\xe9\",\n        \"zumba\": \"Zumba\",\n        \"aerobics\": \"A\\xe9robic\",\n        \"step\": \"Step\",\n        \"dance_fitness\": \"Fitness Danse\",\n        \"bodypump\": \"BodyPump\",\n        \"bootcamp\": \"Bootcamp\",\n        \"yoga\": \"Yoga\",\n        \"pilates\": \"Pilates\",\n        \"stretching\": \"\\xc9tirements\",\n        \"meditation\": \"M\\xe9ditation\",\n        \"breathing_exercises\": \"Exercices de Respiration\",\n        \"football_conditioning\": \"Conditionnement Football\",\n        \"basketball_drills\": \"Exercices Basketball\",\n        \"athletic_performance\": \"Performance Athl\\xe9tique\",\n        \"speed_agility_training\": \"Entra\\xeenement Vitesse et Agilit\\xe9\",\n        \"core_strengthening\": \"Renforcement du Tronc\",\n        \"physiotherapy\": \"Physioth\\xe9rapie\",\n        \"foam_rolling\": \"Rouleau de Massage\",\n        \"mobility_training\": \"Entra\\xeenement de Mobilit\\xe9\",\n        \"post_injury_recovery\": \"R\\xe9cup\\xe9ration Post-Blessure\",\n        \"massage_therapy\": \"Th\\xe9rapie par Massage\",\n        \"weight_loss_plan\": \"Programme de Perte de Poids\",\n        \"muscle_gain_program\": \"Programme de Prise de Muscle\",\n        \"strength_building\": \"D\\xe9veloppement de la Force\",\n        \"senior_fitness\": \"Fitness Senior\",\n        \"pre_post_natal_fitness\": \"Fitness Pr\\xe9/Post-natal\"\n    },\n    ar: {\n        // Navigation\n        dashboard: \"لوحة التحكم\",\n        members: \"الأعضاء\",\n        pos: \"نقطة البيع\",\n        inventory: \"المخزون\",\n        sports: \"الرياضات\",\n        classes: \"الحصص\",\n        reports: \"التقارير\",\n        settings: \"الإعدادات\",\n        logout: \"تسجيل الخروج\",\n        // Member Management\n        \"members_management\": \"إدارة الأعضاء\",\n        \"manage_gym_members\": \"إدارة أعضاء النادي واشتراكاتهم\",\n        \"add_new_member\": \"إضافة عضو جديد\",\n        \"edit_member\": \"تعديل العضو\",\n        \"member_details\": \"تفاصيل العضو\",\n        \"subscription_details\": \"تفاصيل الاشتراك\",\n        \"personal_information\": \"المعلومات الشخصية\",\n        \"contact_information\": \"معلومات الاتصال\",\n        \"full_name\": \"الاسم الكامل\",\n        \"gender\": \"الجنس\",\n        \"age\": \"العمر\",\n        \"phone\": \"الهاتف\",\n        \"email\": \"البريد الإلكتروني\",\n        \"male\": \"ذكر\",\n        \"female\": \"أنثى\",\n        \"pregnant\": \"حامل\",\n        \"situation\": \"الحالة\",\n        \"remarks\": \"ملاحظات\",\n        \"sport\": \"الرياضة\",\n        \"plan_type\": \"نوع الاشتراك\",\n        \"monthly\": \"شهري\",\n        \"quarterly\": \"ربع سنوي\",\n        \"yearly\": \"سنوي\",\n        \"price\": \"السعر\",\n        \"total_amount\": \"المبلغ الإجمالي\",\n        \"save\": \"حفظ\",\n        \"cancel\": \"إلغاء\",\n        \"edit\": \"تعديل\",\n        \"delete\": \"حذف\",\n        \"renew\": \"تجديد\",\n        \"active\": \"نشط\",\n        \"expiring\": \"ينتهي قريباً\",\n        \"expired\": \"منتهي الصلاحية\",\n        \"all\": \"الكل\",\n        \"search_members\": \"البحث عن الأعضاء...\",\n        \"filter_by_status\": \"تصفية حسب الحالة\",\n        \"export_csv\": \"تصدير CSV\",\n        \"export_excel\": \"تصدير Excel\",\n        \"bulk_operations\": \"العمليات المجمعة\",\n        \"select_all\": \"تحديد الكل\",\n        \"selected_count\": \"محدد\",\n        \"delete_selected\": \"حذف المحدد\",\n        \"update_status\": \"تحديث الحالة\",\n        \"no_members_found\": \"لم يتم العثور على أعضاء\",\n        \"try_adjusting_search\": \"حاول تعديل البحث أو المرشحات\",\n        \"get_started_adding\": \"ابدأ بإضافة عضوك الأول\",\n        \"member_added\": \"تم إضافة العضو\",\n        \"member_updated\": \"تم تحديث العضو\",\n        \"member_deleted\": \"تم حذف العضو\",\n        \"subscription_renewed\": \"تم تجديد الاشتراك\",\n        \"add_new_sport\": \"إضافة رياضة جديدة\",\n        \"sport_categories\": \"فئات الرياضة\",\n        // Sports Categories\n        \"gym_strength_training\": \"النادي وتدريب القوة\",\n        \"cardio_endurance\": \"الكارديو والتحمل\",\n        \"boxing_martial_arts\": \"الملاكمة والفنون القتالية\",\n        \"group_classes\": \"الحصص الجماعية\",\n        \"mind_body\": \"العقل والجسم\",\n        \"sports_training\": \"التدريب الرياضي\",\n        \"rehabilitation_recovery\": \"التأهيل والاستشفاء\",\n        \"personal_training\": \"برامج التدريب الشخصي\",\n        // Individual Sports\n        \"bodybuilding\": \"كمال الأجسام\",\n        \"weightlifting\": \"رفع الأثقال\",\n        \"powerlifting\": \"رفع القوة\",\n        \"crossfit\": \"كروس فيت\",\n        \"functional_training\": \"التدريب الوظيفي\",\n        \"calisthenics\": \"التمارين البدنية\",\n        \"treadmill_running\": \"الجري على المشاية\",\n        \"cycling_spinning\": \"ركوب الدراجات/السبينينغ\",\n        \"hiit\": \"التدريب المتقطع عالي الكثافة\",\n        \"stair_climber\": \"تسلق الدرج\",\n        \"rowing_machine\": \"آلة التجديف\",\n        \"boxing\": \"الملاكمة\",\n        \"kickboxing\": \"الكيك بوكسينغ\",\n        \"mma\": \"الفنون القتالية المختلطة\",\n        \"muay_thai\": \"مواي تاي\",\n        \"brazilian_jiu_jitsu\": \"الجوجيتسو البرازيلي\",\n        \"taekwondo\": \"التايكوندو\",\n        \"karate\": \"الكاراتيه\",\n        \"zumba\": \"الزومبا\",\n        \"aerobics\": \"الأيروبيك\",\n        \"step\": \"الستيب\",\n        \"dance_fitness\": \"الرقص الرياضي\",\n        \"bodypump\": \"بودي بامب\",\n        \"bootcamp\": \"المعسكر التدريبي\",\n        \"yoga\": \"اليوغا\",\n        \"pilates\": \"البيلاتس\",\n        \"stretching\": \"التمدد\",\n        \"meditation\": \"التأمل\",\n        \"breathing_exercises\": \"تمارين التنفس\",\n        \"football_conditioning\": \"تكييف كرة القدم\",\n        \"basketball_drills\": \"تدريبات كرة السلة\",\n        \"athletic_performance\": \"الأداء الرياضي\",\n        \"speed_agility_training\": \"تدريب السرعة والرشاقة\",\n        \"core_strengthening\": \"تقوية الجذع\",\n        \"physiotherapy\": \"العلاج الطبيعي\",\n        \"foam_rolling\": \"التدليك بالأسطوانة\",\n        \"mobility_training\": \"تدريب الحركة\",\n        \"post_injury_recovery\": \"التعافي بعد الإصابة\",\n        \"massage_therapy\": \"العلاج بالتدليك\",\n        \"weight_loss_plan\": \"برنامج إنقاص الوزن\",\n        \"muscle_gain_program\": \"برنامج زيادة العضلات\",\n        \"strength_building\": \"بناء القوة\",\n        \"senior_fitness\": \"لياقة كبار السن\",\n        \"pre_post_natal_fitness\": \"لياقة ما قبل/بعد الولادة\"\n    }\n};\nfunction Providers(param) {\n    let { children } = param;\n    _s3();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"en\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session from Supabase\n        const getInitialSession = async ()=>{\n            try {\n                const { data: { session }, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                }\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    setUser(session.user);\n                } else {\n                    // Try to sign in with demo credentials automatically\n                    const { data: signInData, error: signInError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                        email: \"<EMAIL>\",\n                        password: \"admin123\"\n                    });\n                    if (signInError) {\n                        console.log(\"Demo user not found, creating one...\");\n                        // Try to sign up the demo user\n                        const { data: signUpData, error: signUpError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n                            email: \"<EMAIL>\",\n                            password: \"admin123\",\n                            options: {\n                                data: {\n                                    full_name: \"Demo Admin\",\n                                    role: \"admin\"\n                                }\n                            }\n                        });\n                        if (signUpError) {\n                            console.error(\"Error creating demo user:\", signUpError);\n                        } else if (signUpData.user) {\n                            setUser(signUpData.user);\n                        }\n                    } else if (signInData.user) {\n                        setUser(signInData.user);\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state changed:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            var _session_user1;\n            setUser((_session_user1 = session === null || session === void 0 ? void 0 : session.user) !== null && _session_user1 !== void 0 ? _session_user1 : null);\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load theme from localStorage\n        const savedTheme = localStorage.getItem(\"gym-theme\");\n        if (savedTheme) {\n            setTheme(savedTheme);\n            document.documentElement.classList.toggle(\"dark\", savedTheme === \"dark\");\n        }\n        // Load language from localStorage\n        const savedLanguage = localStorage.getItem(\"gym-language\");\n        if (savedLanguage) {\n            setLanguage(savedLanguage);\n            document.documentElement.setAttribute(\"lang\", savedLanguage);\n            document.documentElement.setAttribute(\"dir\", savedLanguage === \"ar\" ? \"rtl\" : \"ltr\");\n        }\n    }, []);\n    const signOut = async ()=>{\n        // Clear all auth data\n        localStorage.removeItem(\"gym-demo-user\");\n        localStorage.removeItem(\"gym-auth-status\");\n        setUser(null);\n        // Redirect to login\n        window.location.href = \"/login\";\n    };\n    const toggleTheme = ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        localStorage.setItem(\"gym-theme\", newTheme);\n        document.documentElement.classList.toggle(\"dark\", newTheme === \"dark\");\n    };\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        localStorage.setItem(\"gym-language\", lang);\n        document.documentElement.setAttribute(\"lang\", lang);\n        document.documentElement.setAttribute(\"dir\", lang === \"ar\" ? \"rtl\" : \"ltr\");\n    };\n    const t = (key)=>{\n        var _translations_language;\n        return ((_translations_language = translations[language]) === null || _translations_language === void 0 ? void 0 : _translations_language[key]) || key;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            loading,\n            signOut\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n            value: {\n                theme,\n                toggleTheme\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n                value: {\n                    language,\n                    setLanguage: handleSetLanguage,\n                    t\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n                lineNumber: 552,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 551,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 550,\n        columnNumber: 5\n    }, this);\n}\n_s3(Providers, \"zVHADGN2gQsHwcsj+EW/UQDHhXo=\");\n_c = Providers;\nvar _c;\n$RefreshReg$(_c, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers.tsx\n"));

/***/ })

});