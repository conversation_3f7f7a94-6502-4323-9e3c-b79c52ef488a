'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/hooks/use-toast'
import { Dumbbell, Eye, EyeOff, Loader2 } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Try to sign in with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        // If user doesn't exist and using demo credentials, create the user
        if (error.message.includes('Invalid login credentials') &&
            email === '<EMAIL>' && password === 'admin123') {

          const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
            email: '<EMAIL>',
            password: 'admin123',
            options: {
              data: {
                full_name: 'Demo Admin',
                role: 'admin'
              }
            }
          })

          if (signUpError) {
            toast({
              title: 'Account Creation Failed',
              description: signUpError.message,
              variant: 'destructive',
            })
          } else {
            toast({
              title: 'Account Created!',
              description: 'Demo admin account created successfully. You are now logged in.',
            })

            setTimeout(() => {
              window.location.href = '/dashboard'
            }, 1000)
          }
        } else {
          toast({
            title: 'Login Failed',
            description: error.message,
            variant: 'destructive',
          })
        }
      } else if (data.user) {
        toast({
          title: 'Welcome Back!',
          description: 'You have been successfully logged in.',
        })

        setTimeout(() => {
          window.location.href = '/dashboard'
        }, 1000)
      }
    } catch (error) {
      console.error('Login error:', error)
      toast({
        title: 'Login Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-white to-gray-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-4">
      <div className="w-full max-w-md">
        {/* Logo and Title */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-black rounded-2xl mb-4 shadow-lg">
            <img
              src="/logo.png"
              alt="Elite Club Logo"
              className="w-16 h-16 object-contain"
            />
          </div>
          <h1 className="text-4xl font-black text-gray-900 dark:text-white mb-2 tracking-tight">
            ÉLITE CLUB
          </h1>
          <p className="text-red-600 dark:text-red-400 font-semibold text-lg">
            RH Champion Club
          </p>
          <p className="text-gray-600 dark:text-gray-400 text-sm mt-2">
            Complete Management System
          </p>
        </div>

        {/* Login Card */}
        <Card className="glass border-white/20 shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
              Welcome Back
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400">
              Sign in to your account to continue
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-6">
              {/* Email Field */}
              <div className="space-y-2">
                <label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <label
                  htmlFor="password"
                  className="text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="w-full px-4 py-3 pr-12 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>
              </div>

              {/* Login Button */}
              <Button
                type="submit"
                variant="gym"
                size="lg"
                className="w-full"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Demo Credentials:
              </p>
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <p>Email: <EMAIL></p>
                <p>Password: admin123</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 space-y-2">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            © 2024 ÉLITE CLUB - All rights reserved
          </p>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <span>Powered by <span className="font-bold text-red-600 dark:text-red-400">iCode DZ</span></span>
            <span className="mx-2">•</span>
            <a
              href="tel:+213551930589"
              className="hover:text-red-600 dark:hover:text-red-400 transition-colors font-medium"
            >
              Tel: +213 551 93 05 89
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
