'use client'

import { useEffect } from 'react'

export default function HomePage() {
  useEffect(() => {
    // Simple redirect logic
    const authStatus = localStorage.getItem('gym-auth-status')
    const demoUser = localStorage.getItem('gym-demo-user')

    if (authStatus === 'authenticated' && demoUser) {
      window.location.href = '/dashboard'
    } else {
      // Auto-set demo authentication for development
      const demoDemoUser = {
        id: 'demo-user',
        email: '<EMAIL>',
        user_metadata: { full_name: 'Demo Admin' }
      }
      localStorage.setItem('gym-demo-user', JSON.stringify(demoDemoUser))
      localStorage.setItem('gym-auth-status', 'authenticated')
      window.location.href = '/dashboard'
    }
  }, [])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="flex flex-col items-center space-y-4">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="text-gray-600 dark:text-gray-400">Loading...</p>
      </div>
    </div>
  )
}
