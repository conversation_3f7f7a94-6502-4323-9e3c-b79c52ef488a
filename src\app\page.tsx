'use client'

import { useEffect } from 'react'
import { useAuth } from '@/components/providers'

export default function HomePage() {
  const { user, loading } = useAuth()

  useEffect(() => {
    if (!loading) {
      if (user) {
        window.location.href = '/dashboard'
      } else {
        window.location.href = '/login'
      }
    }
  }, [user, loading])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="flex flex-col items-center space-y-4">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="text-gray-600 dark:text-gray-400">Loading...</p>
      </div>
    </div>
  )
}
