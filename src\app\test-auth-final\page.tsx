'use client'

import { useAuth } from '@/components/providers'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestAuthFinalPage() {
  const { user, loading, signOut } = useAuth()

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>🔧 Authentication System Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Loading State */}
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900">Loading State:</h3>
              <p className="text-blue-700">{loading ? '⏳ Loading...' : '✅ Loaded'}</p>
            </div>

            {/* User State */}
            <div className="p-4 bg-green-50 rounded-lg">
              <h3 className="font-semibold text-green-900">User State:</h3>
              {user ? (
                <div className="text-green-700">
                  <p>✅ Authenticated</p>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>ID:</strong> {user.id}</p>
                  <p><strong>Created:</strong> {new Date(user.created_at).toLocaleString()}</p>
                </div>
              ) : (
                <p className="text-red-700">❌ Not authenticated</p>
              )}
            </div>

            {/* Actions */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  onClick={() => window.location.href = '/login'}
                  className="bg-blue-500 hover:bg-blue-600"
                >
                  Go to Login
                </Button>
                <Button 
                  onClick={() => window.location.href = '/members'}
                  className="bg-green-500 hover:bg-green-600"
                >
                  Go to Members
                </Button>
                {user && (
                  <Button 
                    onClick={signOut}
                    variant="destructive"
                  >
                    Sign Out
                  </Button>
                )}
              </div>
            </div>

            {/* Instructions */}
            <div className="p-4 bg-yellow-50 rounded-lg">
              <h3 className="font-semibold text-yellow-900">Test Instructions:</h3>
              <ol className="list-decimal list-inside text-yellow-700 space-y-1">
                <li>If not authenticated, click "Go to Login"</li>
                <li>Use demo credentials: <EMAIL> / admin123</li>
                <li>After login, return here to verify authentication</li>
                <li>Test "Go to Members" to check if white screen is fixed</li>
              </ol>
            </div>

            {/* System Status */}
            <div className="p-4 bg-gray-100 rounded-lg">
              <h3 className="font-semibold text-gray-900">System Status:</h3>
              <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                  <p className="text-sm text-gray-600">Authentication:</p>
                  <p className="font-medium">{user ? '✅ Working' : '❌ Not Working'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Loading:</p>
                  <p className="font-medium">{loading ? '⏳ Loading' : '✅ Ready'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
