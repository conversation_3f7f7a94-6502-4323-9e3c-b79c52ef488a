'use client'

import { useAuth } from '@/components/providers'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { Footer } from './footer'
import { cn } from '@/lib/utils'

interface MainLayoutProps {
  children: React.ReactNode
  title?: string
  className?: string
}

export function MainLayout({ children, title, className }: MainLayoutProps) {
  const { user, loading } = useAuth()

  // Check localStorage directly for more reliable auth
  const authStatus = typeof window !== 'undefined' ? localStorage.getItem('gym-auth-status') : null
  const demoUser = typeof window !== 'undefined' ? localStorage.getItem('gym-demo-user') : null

  const isAuthenticated = authStatus === 'authenticated' && demoUser

  // Auto-set demo authentication for development if not authenticated
  if (typeof window !== 'undefined' && !isAuthenticated && !user && !loading) {
    const demoDemoUser = {
      id: 'demo-user',
      email: '<EMAIL>',
      user_metadata: { full_name: 'Demo Admin' }
    }
    localStorage.setItem('gym-demo-user', JSON.stringify(demoDemoUser))
    localStorage.setItem('gym-auth-status', 'authenticated')

    // Reload the page to apply the authentication
    window.location.reload()
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Setting up authentication...
          </h1>
          <div className="w-8 h-8 border-4 border-red-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      <Sidebar />
      <div className="flex flex-col min-h-screen flex-1 overflow-hidden">
        <Header title={title} />
        <main
          className={cn(
            'flex-1 p-4 md:p-6 overflow-auto',
            className
          )}
        >
          {children}
        </main>
        <Footer />
      </div>
    </div>
  )
}
