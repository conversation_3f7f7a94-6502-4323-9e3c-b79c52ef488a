'use client'

import { useAuth } from '@/components/providers'
import { Sidebar } from './sidebar'
import { Header } from './header'
import { Footer } from './footer'
import { cn } from '@/lib/utils'

interface MainLayoutProps {
  children: React.ReactNode
  title?: string
  className?: string
}

export function MainLayout({ children, title, className }: MainLayoutProps) {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    // Redirect to login if not authenticated
    if (typeof window !== 'undefined') {
      window.location.href = '/login'
    }
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      <Sidebar />
      <div className="flex flex-col min-h-screen flex-1 overflow-hidden">
        <Header title={title} />
        <main
          className={cn(
            'flex-1 p-4 md:p-6 overflow-auto',
            className
          )}
        >
          {children}
        </main>
        <Footer />
      </div>
    </div>
  )
}
