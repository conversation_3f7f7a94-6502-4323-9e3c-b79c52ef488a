'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { User } from '@supabase/supabase-js'

interface AuthContextType {
  user: User | null
  loading: boolean
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signOut: async () => {},
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface ThemeContextType {
  theme: 'light' | 'dark'
  toggleTheme: () => void
}

const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  toggleTheme: () => {},
})

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

interface LanguageContextType {
  language: 'en' | 'fr' | 'ar'
  setLanguage: (lang: 'en' | 'fr' | 'ar') => void
  t: (key: string) => string
}

const LanguageContext = createContext<LanguageContextType>({
  language: 'en',
  setLanguage: () => {},
  t: (key: string) => key,
})

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

// Simple translation function (you can replace with a more robust solution)
const translations: Record<string, Record<string, string>> = {
  en: {
    // Navigation
    dashboard: 'Dashboard',
    members: 'Members',
    pos: 'Point of Sale',
    inventory: 'Inventory',
    sports: 'Sports',
    classes: 'Classes',
    reports: 'Reports',
    settings: 'Settings',
    logout: 'Logout',

    // Member Management
    'members_management': 'Members Management',
    'manage_gym_members': 'Manage gym members and their subscriptions',
    'add_new_member': 'Add New Member',
    'edit_member': 'Edit Member',
    'member_details': 'Member Details',
    'subscription_details': 'Subscription Details',
    'personal_information': 'Personal Information',
    'contact_information': 'Contact Information',
    'full_name': 'Full Name',
    'gender': 'Gender',
    'age': 'Age',
    'phone': 'Phone',
    'email': 'Email',
    'male': 'Male',
    'female': 'Female',
    'pregnant': 'Pregnant',
    'situation': 'Situation',
    'remarks': 'Remarks',
    'sport': 'Sport',
    'plan_type': 'Plan Type',
    'monthly': 'Monthly',
    'quarterly': 'Quarterly',
    'yearly': 'Yearly',
    'price': 'Price',
    'total_amount': 'Total Amount',
    'save': 'Save',
    'cancel': 'Cancel',
    'edit': 'Edit',
    'delete': 'Delete',
    'renew': 'Renew',
    'active': 'Active',
    'expiring': 'Expiring',
    'expired': 'Expired',
    'all': 'All',
    'search_members': 'Search members...',
    'filter_by_status': 'Filter by Status',
    'export_csv': 'Export CSV',
    'export_excel': 'Export Excel',
    'bulk_operations': 'Bulk Operations',
    'select_all': 'Select All',
    'selected_count': 'Selected',
    'delete_selected': 'Delete Selected',
    'update_status': 'Update Status',
    'no_members_found': 'No members found',
    'try_adjusting_search': 'Try adjusting your search or filters',
    'get_started_adding': 'Get started by adding your first member',
    'member_added': 'Member Added',
    'member_updated': 'Member Updated',
    'member_deleted': 'Member Deleted',
    'subscription_renewed': 'Subscription Renewed',
    'add_new_sport': 'Add New Sport',
    'sport_categories': 'Sport Categories',

    // Sports Categories
    'gym_strength_training': 'Gym & Strength Training',
    'cardio_endurance': 'Cardio & Endurance',
    'boxing_martial_arts': 'Boxing & Martial Arts',
    'group_classes': 'Group Classes',
    'mind_body': 'Mind & Body',
    'sports_training': 'Sports Training',
    'rehabilitation_recovery': 'Rehabilitation & Recovery',
    'personal_training': 'Personal Training Programs',

    // Individual Sports
    'bodybuilding': 'Bodybuilding',
    'weightlifting': 'Weightlifting',
    'powerlifting': 'Powerlifting',
    'crossfit': 'CrossFit',
    'functional_training': 'Functional Training',
    'calisthenics': 'Calisthenics',
    'treadmill_running': 'Treadmill Running',
    'cycling_spinning': 'Cycling/Spinning',
    'hiit': 'HIIT',
    'stair_climber': 'Stair Climber',
    'rowing_machine': 'Rowing Machine',
    'boxing': 'Boxing',
    'kickboxing': 'Kickboxing',
    'mma': 'MMA',
    'muay_thai': 'Muay Thai',
    'brazilian_jiu_jitsu': 'Brazilian Jiu-Jitsu',
    'taekwondo': 'Taekwondo',
    'karate': 'Karate',
    'zumba': 'Zumba',
    'aerobics': 'Aerobics',
    'step': 'Step',
    'dance_fitness': 'Dance Fitness',
    'bodypump': 'BodyPump',
    'bootcamp': 'Bootcamp',
    'yoga': 'Yoga',
    'pilates': 'Pilates',
    'stretching': 'Stretching',
    'meditation': 'Meditation',
    'breathing_exercises': 'Breathing Exercises',
    'football_conditioning': 'Football Conditioning',
    'basketball_drills': 'Basketball Drills',
    'athletic_performance': 'Athletic Performance',
    'speed_agility_training': 'Speed & Agility Training',
    'core_strengthening': 'Core Strengthening',
    'physiotherapy': 'Physiotherapy',
    'foam_rolling': 'Foam Rolling',
    'mobility_training': 'Mobility Training',
    'post_injury_recovery': 'Post-injury Recovery',
    'massage_therapy': 'Massage Therapy',
    'weight_loss_plan': 'Weight Loss Plan',
    'muscle_gain_program': 'Muscle Gain Program',
    'strength_building': 'Strength Building',
    'senior_fitness': 'Senior Fitness',
    'pre_post_natal_fitness': 'Pre/Post-natal Fitness',
  },
  fr: {
    // Navigation
    dashboard: 'Tableau de bord',
    members: 'Membres',
    pos: 'Point de vente',
    inventory: 'Inventaire',
    sports: 'Sports',
    classes: 'Cours',
    reports: 'Rapports',
    settings: 'Paramètres',
    logout: 'Déconnexion',

    // Member Management
    'members_management': 'Gestion des Membres',
    'manage_gym_members': 'Gérer les membres de la salle et leurs abonnements',
    'add_new_member': 'Ajouter un Nouveau Membre',
    'edit_member': 'Modifier le Membre',
    'member_details': 'Détails du Membre',
    'subscription_details': 'Détails de l\'Abonnement',
    'personal_information': 'Informations Personnelles',
    'contact_information': 'Informations de Contact',
    'full_name': 'Nom Complet',
    'gender': 'Sexe',
    'age': 'Âge',
    'phone': 'Téléphone',
    'email': 'Email',
    'male': 'Homme',
    'female': 'Femme',
    'pregnant': 'Enceinte',
    'situation': 'Situation',
    'remarks': 'Remarques',
    'sport': 'Sport',
    'plan_type': 'Type d\'Abonnement',
    'monthly': 'Mensuel',
    'quarterly': 'Trimestriel',
    'yearly': 'Annuel',
    'price': 'Prix',
    'total_amount': 'Montant Total',
    'save': 'Enregistrer',
    'cancel': 'Annuler',
    'edit': 'Modifier',
    'delete': 'Supprimer',
    'renew': 'Renouveler',
    'active': 'Actif',
    'expiring': 'Expire Bientôt',
    'expired': 'Expiré',
    'all': 'Tous',
    'search_members': 'Rechercher des membres...',
    'filter_by_status': 'Filtrer par Statut',
    'export_csv': 'Exporter CSV',
    'export_excel': 'Exporter Excel',
    'bulk_operations': 'Opérations en Lot',
    'select_all': 'Tout Sélectionner',
    'selected_count': 'Sélectionnés',
    'delete_selected': 'Supprimer Sélectionnés',
    'update_status': 'Mettre à Jour le Statut',
    'no_members_found': 'Aucun membre trouvé',
    'try_adjusting_search': 'Essayez d\'ajuster votre recherche ou vos filtres',
    'get_started_adding': 'Commencez par ajouter votre premier membre',
    'member_added': 'Membre Ajouté',
    'member_updated': 'Membre Mis à Jour',
    'member_deleted': 'Membre Supprimé',
    'subscription_renewed': 'Abonnement Renouvelé',
    'add_new_sport': 'Ajouter un Nouveau Sport',
    'sport_categories': 'Catégories de Sports',

    // Sports Categories
    'gym_strength_training': 'Musculation et Force',
    'cardio_endurance': 'Cardio et Endurance',
    'boxing_martial_arts': 'Boxe et Arts Martiaux',
    'group_classes': 'Cours Collectifs',
    'mind_body': 'Corps et Esprit',
    'sports_training': 'Entraînement Sportif',
    'rehabilitation_recovery': 'Rééducation et Récupération',
    'personal_training': 'Programmes d\'Entraînement Personnel',

    // Individual Sports
    'bodybuilding': 'Bodybuilding',
    'weightlifting': 'Haltérophilie',
    'powerlifting': 'Force Athlétique',
    'crossfit': 'CrossFit',
    'functional_training': 'Entraînement Fonctionnel',
    'calisthenics': 'Callisthénie',
    'treadmill_running': 'Course sur Tapis',
    'cycling_spinning': 'Cyclisme/Spinning',
    'hiit': 'HIIT',
    'stair_climber': 'Escalier',
    'rowing_machine': 'Rameur',
    'boxing': 'Boxe',
    'kickboxing': 'Kickboxing',
    'mma': 'MMA',
    'muay_thai': 'Muay Thai',
    'brazilian_jiu_jitsu': 'Jiu-Jitsu Brésilien',
    'taekwondo': 'Taekwondo',
    'karate': 'Karaté',
    'zumba': 'Zumba',
    'aerobics': 'Aérobic',
    'step': 'Step',
    'dance_fitness': 'Fitness Danse',
    'bodypump': 'BodyPump',
    'bootcamp': 'Bootcamp',
    'yoga': 'Yoga',
    'pilates': 'Pilates',
    'stretching': 'Étirements',
    'meditation': 'Méditation',
    'breathing_exercises': 'Exercices de Respiration',
    'football_conditioning': 'Conditionnement Football',
    'basketball_drills': 'Exercices Basketball',
    'athletic_performance': 'Performance Athlétique',
    'speed_agility_training': 'Entraînement Vitesse et Agilité',
    'core_strengthening': 'Renforcement du Tronc',
    'physiotherapy': 'Physiothérapie',
    'foam_rolling': 'Rouleau de Massage',
    'mobility_training': 'Entraînement de Mobilité',
    'post_injury_recovery': 'Récupération Post-Blessure',
    'massage_therapy': 'Thérapie par Massage',
    'weight_loss_plan': 'Programme de Perte de Poids',
    'muscle_gain_program': 'Programme de Prise de Muscle',
    'strength_building': 'Développement de la Force',
    'senior_fitness': 'Fitness Senior',
    'pre_post_natal_fitness': 'Fitness Pré/Post-natal',
  },
  ar: {
    // Navigation
    dashboard: 'لوحة التحكم',
    members: 'الأعضاء',
    pos: 'نقطة البيع',
    inventory: 'المخزون',
    sports: 'الرياضات',
    classes: 'الحصص',
    reports: 'التقارير',
    settings: 'الإعدادات',
    logout: 'تسجيل الخروج',

    // Member Management
    'members_management': 'إدارة الأعضاء',
    'manage_gym_members': 'إدارة أعضاء النادي واشتراكاتهم',
    'add_new_member': 'إضافة عضو جديد',
    'edit_member': 'تعديل العضو',
    'member_details': 'تفاصيل العضو',
    'subscription_details': 'تفاصيل الاشتراك',
    'personal_information': 'المعلومات الشخصية',
    'contact_information': 'معلومات الاتصال',
    'full_name': 'الاسم الكامل',
    'gender': 'الجنس',
    'age': 'العمر',
    'phone': 'الهاتف',
    'email': 'البريد الإلكتروني',
    'male': 'ذكر',
    'female': 'أنثى',
    'pregnant': 'حامل',
    'situation': 'الحالة',
    'remarks': 'ملاحظات',
    'sport': 'الرياضة',
    'plan_type': 'نوع الاشتراك',
    'monthly': 'شهري',
    'quarterly': 'ربع سنوي',
    'yearly': 'سنوي',
    'price': 'السعر',
    'total_amount': 'المبلغ الإجمالي',
    'save': 'حفظ',
    'cancel': 'إلغاء',
    'edit': 'تعديل',
    'delete': 'حذف',
    'renew': 'تجديد',
    'active': 'نشط',
    'expiring': 'ينتهي قريباً',
    'expired': 'منتهي الصلاحية',
    'all': 'الكل',
    'search_members': 'البحث عن الأعضاء...',
    'filter_by_status': 'تصفية حسب الحالة',
    'export_csv': 'تصدير CSV',
    'export_excel': 'تصدير Excel',
    'bulk_operations': 'العمليات المجمعة',
    'select_all': 'تحديد الكل',
    'selected_count': 'محدد',
    'delete_selected': 'حذف المحدد',
    'update_status': 'تحديث الحالة',
    'no_members_found': 'لم يتم العثور على أعضاء',
    'try_adjusting_search': 'حاول تعديل البحث أو المرشحات',
    'get_started_adding': 'ابدأ بإضافة عضوك الأول',
    'member_added': 'تم إضافة العضو',
    'member_updated': 'تم تحديث العضو',
    'member_deleted': 'تم حذف العضو',
    'subscription_renewed': 'تم تجديد الاشتراك',
    'add_new_sport': 'إضافة رياضة جديدة',
    'sport_categories': 'فئات الرياضة',

    // Sports Categories
    'gym_strength_training': 'النادي وتدريب القوة',
    'cardio_endurance': 'الكارديو والتحمل',
    'boxing_martial_arts': 'الملاكمة والفنون القتالية',
    'group_classes': 'الحصص الجماعية',
    'mind_body': 'العقل والجسم',
    'sports_training': 'التدريب الرياضي',
    'rehabilitation_recovery': 'التأهيل والاستشفاء',
    'personal_training': 'برامج التدريب الشخصي',

    // Individual Sports
    'bodybuilding': 'كمال الأجسام',
    'weightlifting': 'رفع الأثقال',
    'powerlifting': 'رفع القوة',
    'crossfit': 'كروس فيت',
    'functional_training': 'التدريب الوظيفي',
    'calisthenics': 'التمارين البدنية',
    'treadmill_running': 'الجري على المشاية',
    'cycling_spinning': 'ركوب الدراجات/السبينينغ',
    'hiit': 'التدريب المتقطع عالي الكثافة',
    'stair_climber': 'تسلق الدرج',
    'rowing_machine': 'آلة التجديف',
    'boxing': 'الملاكمة',
    'kickboxing': 'الكيك بوكسينغ',
    'mma': 'الفنون القتالية المختلطة',
    'muay_thai': 'مواي تاي',
    'brazilian_jiu_jitsu': 'الجوجيتسو البرازيلي',
    'taekwondo': 'التايكوندو',
    'karate': 'الكاراتيه',
    'zumba': 'الزومبا',
    'aerobics': 'الأيروبيك',
    'step': 'الستيب',
    'dance_fitness': 'الرقص الرياضي',
    'bodypump': 'بودي بامب',
    'bootcamp': 'المعسكر التدريبي',
    'yoga': 'اليوغا',
    'pilates': 'البيلاتس',
    'stretching': 'التمدد',
    'meditation': 'التأمل',
    'breathing_exercises': 'تمارين التنفس',
    'football_conditioning': 'تكييف كرة القدم',
    'basketball_drills': 'تدريبات كرة السلة',
    'athletic_performance': 'الأداء الرياضي',
    'speed_agility_training': 'تدريب السرعة والرشاقة',
    'core_strengthening': 'تقوية الجذع',
    'physiotherapy': 'العلاج الطبيعي',
    'foam_rolling': 'التدليك بالأسطوانة',
    'mobility_training': 'تدريب الحركة',
    'post_injury_recovery': 'التعافي بعد الإصابة',
    'massage_therapy': 'العلاج بالتدليك',
    'weight_loss_plan': 'برنامج إنقاص الوزن',
    'muscle_gain_program': 'برنامج زيادة العضلات',
    'strength_building': 'بناء القوة',
    'senior_fitness': 'لياقة كبار السن',
    'pre_post_natal_fitness': 'لياقة ما قبل/بعد الولادة',
  },
}

export function Providers({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [theme, setTheme] = useState<'light' | 'dark'>('light')
  const [language, setLanguage] = useState<'en' | 'fr' | 'ar'>('en')

  useEffect(() => {
    // Simple localStorage-based auth check
    const checkAuth = () => {
      try {
        const authStatus = localStorage.getItem('gym-auth-status')
        const demoUser = localStorage.getItem('gym-demo-user')

        if (authStatus === 'authenticated' && demoUser) {
          setUser(JSON.parse(demoUser))
        } else {
          // Auto-set demo authentication for development
          const demoDemoUser = {
            id: 'demo-user',
            email: '<EMAIL>',
            user_metadata: { full_name: 'Demo Admin' }
          }
          localStorage.setItem('gym-demo-user', JSON.stringify(demoDemoUser))
          localStorage.setItem('gym-auth-status', 'authenticated')
          setUser(demoDemoUser)
        }
      } catch (error) {
        console.log('Error checking auth:', error)
        // Even on error, set demo user for development
        const demoDemoUser = {
          id: 'demo-user',
          email: '<EMAIL>',
          user_metadata: { full_name: 'Demo Admin' }
        }
        localStorage.setItem('gym-demo-user', JSON.stringify(demoDemoUser))
        localStorage.setItem('gym-auth-status', 'authenticated')
        setUser(demoDemoUser)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  useEffect(() => {
    // Load theme from localStorage
    const savedTheme = localStorage.getItem('gym-theme') as 'light' | 'dark'
    if (savedTheme) {
      setTheme(savedTheme)
      document.documentElement.classList.toggle('dark', savedTheme === 'dark')
    }

    // Load language from localStorage
    const savedLanguage = localStorage.getItem('gym-language') as 'en' | 'fr' | 'ar'
    if (savedLanguage) {
      setLanguage(savedLanguage)
      document.documentElement.setAttribute('lang', savedLanguage)
      document.documentElement.setAttribute('dir', savedLanguage === 'ar' ? 'rtl' : 'ltr')
    }
  }, [])

  const signOut = async () => {
    // Clear all auth data
    localStorage.removeItem('gym-demo-user')
    localStorage.removeItem('gym-auth-status')
    setUser(null)

    // Redirect to login
    window.location.href = '/login'
  }

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
    localStorage.setItem('gym-theme', newTheme)
    document.documentElement.classList.toggle('dark', newTheme === 'dark')
  }

  const handleSetLanguage = (lang: 'en' | 'fr' | 'ar') => {
    setLanguage(lang)
    localStorage.setItem('gym-language', lang)
    document.documentElement.setAttribute('lang', lang)
    document.documentElement.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr')
  }

  const t = (key: string): string => {
    return translations[language]?.[key] || key
  }

  return (
    <AuthContext.Provider value={{ user, loading, signOut }}>
      <ThemeContext.Provider value={{ theme, toggleTheme }}>
        <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
          {children}
        </LanguageContext.Provider>
      </ThemeContext.Provider>
    </AuthContext.Provider>
  )
}
